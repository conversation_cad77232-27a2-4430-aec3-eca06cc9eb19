package com.yunqu.park.iot.service.command;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.*;
import com.yunqu.park.iot.service.command.impl.*;
import org.springframework.stereotype.Component;

/**
 * 指令工厂类.
 * 使用工厂模式, 根据指令类型和参数动态创建对应的指令策略对象.
 */
@Component
public class CommandFactory {

    /**
     * 创建指令策略对象.
     *
     * @param type   指令类型
     * @param params 指令参数 (可能为NoParams的实例)
     * @return 对应的ICommand实现
     * @throws IllegalArgumentException 如果指令类型不被支持
     */
    public ICommand createCommand(CommandType type, CommandParams params) {
        return switch (type) {
            // --- 无参数指令 ---
            case REBOOT -> new RebootCommand();
            case FACTORY_RESET -> new FactoryResetCommand();
            case QUERY_LOCATION -> new QueryLocationCommand();
            case QUERY_LINK_ADDRESS -> new QueryLinkAddressCommand();
            case QUERY_PARAMETERS -> new QueryParametersCommand();
            case QUERY_STATUS -> new QueryStatusCommand();
            case QUERY_VERSION -> new QueryVersionCommand();
            case QUERY_ICCID -> new QueryIccidCommand();
            case QUERY_NETWORK -> new QueryNetworkCommand();
            case QUERY_MODULE_VERSION -> new QueryModuleVersionCommand();
            case QUERY_VIBRATION_SENSITIVITY -> new QueryVibrationSensitivityCommand();
            case QUERY_MILEAGE_STATISTICS -> new QueryMileageStatisticsCommand();
            case QUERY_NETWORK_MODE -> new QueryNetworkModeCommand();
            case CLEAR_MILEAGE -> new ClearMileageCommand();
            case DELETE_CENTER_NUMBER -> new DeleteCenterNumberCommand();
            case SET_DISARM_STATE -> new SetDisarmStateCommand();
            case CLEAR_DATA -> new ClearDataCommand();

            // --- 有参数指令 ---
            case SET_IP -> new SetIpCommand((SetIpParams) params);
            case SET_DUAL_IP -> new SetDualIpCommand((SetDualIpParams) params);
            case SET_APN -> new SetApnCommand((SetApnParams) params);
            case SET_UPLOAD_INTERVAL -> new SetUploadIntervalCommand((SetUploadIntervalParams) params);
            case SET_HEARTBEAT_INTERVAL -> new SetHeartbeatIntervalCommand((SetHeartbeatIntervalParams) params);
            case SET_SOS_NUMBER -> new SetSosNumberCommand((SetSosNumberParams) params);
            case DELETE_SOS_NUMBER -> new DeleteSosNumberCommand((DeleteSosNumberParams) params);
            case SET_CENTER_NUMBER -> new SetCenterNumberCommand((SetCenterNumberParams) params);
            case CONTROL_RELAY -> new ControlRelayCommand((ControlRelayParams) params);
            case SET_TIMEZONE -> new SetTimezoneCommand((SetTimezoneParams) params);
            case SET_LANGUAGE -> new SetLanguageCommand((SetLanguageParams) params);
            case SET_AUTO_ARM -> new SetAutoArmCommand((SetAutoArmParams) params);
            case SET_ANGLE_UPLOAD -> new SetAngleUploadCommand((SetAngleUploadParams) params);
            case SET_ANGLE_VALUE -> new SetAngleValueCommand((SetAngleValueParams) params);
            case SET_UPLOAD_TIMEZONE -> new SetUploadTimezoneCommand((SetUploadTimezoneParams) params);
            case SET_VIBRATION_SENSITIVITY -> new SetVibrationSensitivityCommand((SetVibrationSensitivityParams) params);
            case SET_POWER_ALARM -> new SetPowerAlarmCommand((SetAlarmParams) params);
            case SET_ACC_ALARM -> new SetAccAlarmCommand((SetAlarmParams) params);
            case SET_VIBRATION_ALARM -> new SetVibrationAlarmCommand((SetAlarmParams) params);
            case SET_LOW_BATTERY_ALARM -> new SetLowBatteryAlarmCommand((SetAlarmParams) params);
            case SET_SPEED_ALARM -> new SetSpeedAlarmCommand((SetSpeedAlarmParams) params);
            case SET_MOVING_ALARM -> new SetMovingAlarmCommand((SetMovingAlarmParams) params);
            case SET_HARSH_ACCELERATION_ALARM -> new SetHarshAccelerationAlarmCommand((SetDrivingBehaviorParams) params);
            case SET_HARSH_BRAKING_ALARM -> new SetHarshBrakingAlarmCommand((SetDrivingBehaviorParams) params);
            case SET_HARSH_TURNING_ALARM -> new SetHarshTurningAlarmCommand((SetDrivingBehaviorParams) params);
            case SET_COLLISION_ALARM -> new SetCollisionAlarmCommand((SetDrivingBehaviorParams) params);
            case SWITCH_MILEAGE_MODE -> new SwitchMileageModeCommand((SwitchMileageModeParams) params);
            case SET_MILEAGE_STATISTICS -> new SetMileageStatsCommand((SetMileageStatsParams) params);
            case SET_STATIC_SLEEP -> new SetStaticSleepCommand((SetStaticSleepParams) params);
            case SET_SPEED_MEASUREMENT_MODE -> new SetSpeedMeasurementModeCommand((SetSpeedMeasurementModeParams) params);
            case SET_GPS_MODE -> new SetGpsModeCommand((SetGpsModeParams) params);
            case SET_GPS_UPLOAD_DURATION -> new SetGpsUploadDurationCommand((SetGpsUploadDurationParams) params);
            case SET_GPRS_SWITCH -> new SetGprsSwitchCommand((SetGprsSwitchParams) params);
            case SET_SATELLITE_LOCK_SWITCH -> new SetSatelliteLockSwitchCommand((SetSatelliteLockSwitchParams) params);
            case SET_DISTANCE_UPLOAD -> new SetDistanceUploadCommand((SetDistanceUploadParams) params);
            case SET_GPS_SLEEP_WORK -> new SetGpsSleepWorkCommand((SetGpsSleepWorkParams) params);
            case SET_STATIC_UPLOAD_INTERVAL -> new SetStaticUploadIntervalCommand((SetStaticUploadIntervalParams) params);
            
            default -> throw new IllegalArgumentException("不支持的指令类型: " + type);
        };
    }
}