package com.yunqu.park.iot.service.command;

import com.yunqu.park.iot.model.command.CommandType;

/**
 * 指令策略接口.
 * 定义了所有具体指令类必须实现的共同行为.
 * 这是策略模式的核心.
 */
public interface ICommand {

    /**
     * 获取该指令的唯一类型标识.
     *
     * @return CommandType 枚举实例
     */
    CommandType getType();

    /**
     * 根据指令参数构建最终要发送给设备的原始指令字符串.
     *
     * @return 符合协议规范的指令字符串
     */
    String build();

    /**
     * 获取用于日志记录和历史追溯的指令业务描述.
     *
     * @return 指令的中文描述, 通常包含关键参数
     */
    String getDescription();
}