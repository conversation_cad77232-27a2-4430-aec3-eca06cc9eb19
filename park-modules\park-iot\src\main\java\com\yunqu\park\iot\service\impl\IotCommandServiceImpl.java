package com.yunqu.park.iot.service.impl;

import com.yunqu.park.common.redis.utils.RedisUtils;
import com.yunqu.park.iot.model.command.CommandRequest;
import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.*;
import com.yunqu.park.iot.model.dto.BatchCommandRequest;
import com.yunqu.park.iot.model.dto.command.*;
import com.yunqu.park.iot.netty.manager.DeviceConnectionManager;
import com.yunqu.park.iot.service.IIotCommandService;
import com.yunqu.park.iot.service.command.CommandFactory;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;

/**
 * IoT设备指令服务实现 (V2.0 - 已重构)
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IotCommandServiceImpl implements IIotCommandService {

    private final DeviceConnectionManager connectionManager;
    private final CommandFactory commandFactory;

    private static final String COMMAND_CACHE_PREFIX = "iot:command:";
    private static final String COMMAND_HISTORY_PREFIX = "iot:command:history:";

    @Override
    public void sendCommand(CommandRequest request) {
        executeCommand(request.getImei(), request.getCommandType(), request.getParams());
    }

    @Override
    public Boolean executeCommand(String imei, CommandType type, CommandParams params) {
        try {
            log.info("[COMMAND-EXEC] 🚀 Processing command: IMEI={}, Type={}", imei, type);

            if (!isDeviceOnline(imei)) {
                log.warn("[COMMAND-EXEC] ❌ Device offline, cannot send command: IMEI={}, Type={}", imei, type);
                return false;
            }

            // 1. 使用工厂创建指令策略对象
            ICommand commandStrategy = commandFactory.createCommand(type, params);
            // 2. 构建指令字符串
            String commandString = commandStrategy.build();
            log.debug("[COMMAND-EXEC] Command built: IMEI={}, Command={}", imei, commandString);

            // 3. 发送指令到设备
            boolean result = sendCommandToDevice(imei, type.name(), commandString);

            if (result) {
                // 4. 记录指令历史
                recordCommandHistory(imei, type.name(), commandString, commandStrategy.getDescription());
                log.info("[COMMAND-EXEC] ✅ Command sent successfully: IMEI={}, Type={}", imei, type);
            } else {
                log.error("[COMMAND-EXEC] ❌ Failed to send command: IMEI={}, Type={}", imei, type);
            }

            return result;
        } catch (Exception e) {
            log.error("[COMMAND-EXEC] ❌ Exception sending command: IMEI={}, Type={}, Error={}",
                imei, type, e.getMessage(), e);
            return false;
        }
    }

    // --- 兼容旧接口的适配器方法 ---

    @Override
    @Deprecated
    public Boolean sendRestartCommand(String imei) {
        return executeCommand(imei, CommandType.REBOOT, new NoParams());
    }

    @Override
    @Deprecated
    public Boolean sendResetCommand(String imei) {
        return executeCommand(imei, CommandType.FACTORY_RESET, new NoParams());
    }

    @Override
    @Deprecated
    public Boolean setReportInterval(String imei, Integer interval) {
        SetUploadIntervalParams params = new SetUploadIntervalParams();
        params.setInterval(interval);
        return executeCommand(imei, CommandType.SET_UPLOAD_INTERVAL, params);
    }

    @Override
    @Deprecated
    public Boolean setApnConfig(String imei, String apn, String username, String password) {
        SetApnParams params = new SetApnParams();
        params.setApn(apn);
        params.setUser(username);
        params.setPass(password);
        return executeCommand(imei, CommandType.SET_APN, params);
    }

    @Override
    @Deprecated
    public Boolean setServerAddress(String imei, String serverIp, Integer port) {
        SetIpParams params = new SetIpParams();
        params.setMode(0); // 假设旧接口默认使用主服务器模式
        params.setAddress(serverIp);
        params.setPort(port);
        return executeCommand(imei, CommandType.SET_IP, params);
    }

    @Override
    @Deprecated
    public Boolean queryDeviceStatus(String imei) {
        return executeCommand(imei, CommandType.QUERY_STATUS, new NoParams());
    }

    @Override
    @Deprecated
    public Boolean setTimezone(String imei, Integer timezone) {
        SetTimezoneParams params = new SetTimezoneParams();
        params.setDirection(timezone >= 0 ? "E" : "W");
        params.setHours(Math.abs(timezone));
        params.setMinutes(0);
        return executeCommand(imei, CommandType.SET_TIMEZONE, params);
    }

    @Override
    @Deprecated
    public Boolean setWorkMode(String imei, Integer mode) {
        // 注意: 协议中没有找到完全对应的 'MODE,X#' 指令, 此处为示意
        log.warn("The 'setWorkMode' method is deprecated and may not have a direct equivalent in the new protocol.");
        return false;
    }

    @Override
    @Deprecated
    public Boolean sendCustomCommand(String imei, String command) {
        log.warn("The 'sendCustomCommand' method is deprecated. Custom commands should be handled differently now.");
        // 自定义指令在新架构下应有特定处理方式, 此处返回false
        return false;
    }

    @Override
    public Object getCommandHistory(String imei, Integer limit) {
        try {
            String cacheKey = COMMAND_HISTORY_PREFIX + imei;
            List<Object> history = RedisUtils.getCacheList(cacheKey);

            if (history == null || history.isEmpty()) {
                return new ArrayList<>();
            }

            int size = Math.min(limit, history.size());
            return history.subList(0, size);

        } catch (Exception e) {
            log.error("Failed to get command history for device {}: {}", imei, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Object sendBatchCommand(BatchCommandRequest request) {
        Map<String, Object> result = new HashMap<>();
        List<String> successList = new ArrayList<>();
        List<String> failList = new ArrayList<>();

        try {
            String[] imeis = request.getImeis();
            String commandTypeStr = request.getCommandType();
            CommandType commandType = CommandType.valueOf(commandTypeStr.toUpperCase());

            for (String imei : imeis) {
                // 批量指令仅支持无参数指令
                boolean success = executeCommand(imei, commandType, new NoParams());
                if (success) {
                    successList.add(imei);
                } else {
                    failList.add(imei);
                }
            }

            result.put("total", imeis.length);
            result.put("success", successList.size());
            result.put("fail", failList.size());
            result.put("successList", successList);
            result.put("failList", failList);

        } catch (Exception e) {
            log.error("Failed to send batch command: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    private boolean isDeviceOnline(String imei) {
        return connectionManager.isDeviceOnline(imei);
    }

    private boolean sendCommandToDevice(String imei, String commandType, String command) {
        try {
            boolean result = connectionManager.sendCommandToDevice(imei, command);

            if (result) {
                String cacheKey = COMMAND_CACHE_PREFIX + imei;
                Map<String, Object> commandData = new HashMap<>();
                commandData.put("type", commandType);
                commandData.put("command", command);
                commandData.put("timestamp", System.currentTimeMillis());
                commandData.put("status", "sent");
                RedisUtils.setCacheObject(cacheKey, commandData, Duration.ofMinutes(5));
            }
            return result;
        } catch (Exception e) {
            log.error("Failed to send command to device {}: {}", imei, e.getMessage(), e);
            return false;
        }
    }

    private void recordCommandHistory(String imei, String type, String command, String description) {
        try {
            String cacheKey = COMMAND_HISTORY_PREFIX + imei;
            Map<String, Object> record = new HashMap<>();
            record.put("type", type);
            record.put("command", command);
            record.put("description", description);
            record.put("timestamp", System.currentTimeMillis());
            record.put("time", new Date());

            List<Object> history = RedisUtils.getCacheList(cacheKey);
            if (history == null) {
                history = new ArrayList<>();
            }
            history.add(0, record);
            if (history.size() > 100) {
                history = history.subList(0, 100);
            }
            RedisUtils.setCacheList(cacheKey, history);
        } catch (Exception e) {
            log.error("Failed to record command history for device {}: {}", imei, e.getMessage(), e);
        }
    }

    // ========== 新的独立指令接口实现 ==========

    @Override
    public void reboot(RebootRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.REBOOT);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setIp(SetIpRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_IP);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void queryLocation(QueryLocationRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.QUERY_LOCATION);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void factoryReset(FactoryResetRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.FACTORY_RESET);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void queryLinkAddress(QueryLinkAddressRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.QUERY_LINK_ADDRESS);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void queryParameters(QueryParametersRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.QUERY_PARAMETERS);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void queryStatus(QueryStatusRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.QUERY_STATUS);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void queryVersion(QueryVersionRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.QUERY_VERSION);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void queryIccid(QueryIccidRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.QUERY_ICCID);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void queryNetwork(QueryNetworkRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.QUERY_NETWORK);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void queryModuleVersion(QueryModuleVersionRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.QUERY_MODULE_VERSION);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void queryVibrationSensitivity(QueryVibrationSensitivityRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.QUERY_VIBRATION_SENSITIVITY);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void queryMileageStatistics(QueryMileageStatisticsRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.QUERY_MILEAGE_STATISTICS);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void queryNetworkMode(QueryNetworkModeRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.QUERY_NETWORK_MODE);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void clearMileage(ClearMileageRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.CLEAR_MILEAGE);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void deleteCenterNumber(DeleteCenterNumberRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.DELETE_CENTER_NUMBER);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setDisarmState(SetDisarmStateRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_DISARM_STATE);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void clearData(ClearDataRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.CLEAR_DATA);
        commandRequest.setParams(new NoParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setDualIp(SetDualIpRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_DUAL_IP);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setApn(SetApnRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_APN);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setUploadInterval(SetUploadIntervalRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_UPLOAD_INTERVAL);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    // 添加所有剩余的有参数指令方法实现
    @Override
    public void setHeartbeatInterval(SetHeartbeatIntervalRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_HEARTBEAT_INTERVAL);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setSosNumber(SetSosNumberRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_SOS_NUMBER);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void deleteSosNumber(DeleteSosNumberRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.DELETE_SOS_NUMBER);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setCenterNumber(SetCenterNumberRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_CENTER_NUMBER);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void controlRelay(ControlRelayRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.CONTROL_RELAY);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setTimezone(SetTimezoneRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_TIMEZONE);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setLanguage(SetLanguageRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_LANGUAGE);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setAutoArm(SetAutoArmRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_AUTO_ARM);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setAngleUpload(SetAngleUploadRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_ANGLE_UPLOAD);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setAngleValue(SetAngleValueRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_ANGLE_VALUE);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setUploadTimezone(SetUploadTimezoneRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_UPLOAD_TIMEZONE);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setVibrationSensitivity(SetVibrationSensitivityRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_VIBRATION_SENSITIVITY);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setVibrationAlarm(SetVibrationAlarmRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_VIBRATION_ALARM);
        commandRequest.setParams(request.getParams());
        sendCommand(commandRequest);
    }

    @Override
    public void setPowerAlarm(SetPowerAlarmRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_POWER_ALARM);

        SetAlarmParams params = new SetAlarmParams();
        params.setState(request.getEnabled() ? "ON" : "OFF");
        params.setMode(1); // 默认模式
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setLowBatteryAlarm(SetLowBatteryAlarmRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_LOW_BATTERY_ALARM);

        SetAlarmParams params = new SetAlarmParams();
        params.setState(request.getEnabled() ? "ON" : "OFF");
        params.setMode(request.getThreshold() != null ? request.getThreshold() : 20); // 使用threshold作为mode
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setSpeedAlarm(SetSpeedAlarmRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_SPEED_ALARM);

        SetSpeedAlarmParams params = new SetSpeedAlarmParams();
        params.setState(request.getEnabled() ? "ON" : "OFF");
        params.setSpeed(request.getSpeedLimit());
        params.setMode(1); // 默认模式
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setMovingAlarm(SetMovingAlarmRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_MOVING_ALARM);

        SetMovingAlarmParams params = new SetMovingAlarmParams();
        params.setState(request.getEnabled() ? "ON" : "OFF");
        params.setRadius(request.getSensitivity() != null ? request.getSensitivity() * 10 : 100); // 将灵敏度转换为半径
        params.setMode(1); // 默认模式
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setAccAlarm(SetAccAlarmRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_ACC_ALARM);

        SetAlarmParams params = new SetAlarmParams();
        params.setState(request.getEnabled() ? "ON" : "OFF");
        params.setMode(1); // 默认模式
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setHarshAccelerationAlarm(SetHarshAccelerationAlarmRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_HARSH_ACCELERATION_ALARM);
        
        SetDrivingBehaviorParams params = new SetDrivingBehaviorParams();
        params.setValue(request.getThreshold());
        params.setMode(request.getEnabled() ? 1 : 0);
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setHarshBrakingAlarm(SetHarshBrakingAlarmRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_HARSH_BRAKING_ALARM);
        
        SetDrivingBehaviorParams params = new SetDrivingBehaviorParams();
        params.setValue(request.getThreshold());
        params.setMode(request.getEnabled() ? 1 : 0);
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setHarshTurningAlarm(SetHarshTurningAlarmRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_HARSH_TURNING_ALARM);
        
        SetDrivingBehaviorParams params = new SetDrivingBehaviorParams();
        params.setValue(request.getThreshold());
        params.setMode(request.getEnabled() ? 1 : 0);
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setCollisionAlarm(SetCollisionAlarmRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_COLLISION_ALARM);
        
        SetAlarmParams params = new SetAlarmParams();
        params.setState(request.getEnabled() ? "ON" : "OFF");
        params.setMode(request.getSensitivity() != null ? request.getSensitivity() : 5);
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void switchMileageMode(SwitchMileageModeRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SWITCH_MILEAGE_MODE);
        
        SwitchMileageModeParams params = new SwitchMileageModeParams();
        params.setMode(request.getMode());
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setMileageStatistics(SetMileageStatisticsRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_MILEAGE_STATISTICS);
        
        SetMileageStatsParams params = new SetMileageStatsParams();
        params.setState(request.getEnabled() ? 1 : 0);
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setStaticSleep(SetStaticSleepRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_STATIC_SLEEP);
        
        SetStaticSleepParams params = new SetStaticSleepParams();
        params.setCommand(request.getEnabled() ? "SLPON" : "SLPOFF");
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setSpeedMeasurementMode(SetSpeedMeasurementModeRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_SPEED_MEASUREMENT_MODE);
        
        SetSpeedMeasurementModeParams params = new SetSpeedMeasurementModeParams();
        params.setMode(request.getMode());
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setGpsMode(SetGpsModeRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_GPS_MODE);
        
        SetGpsModeParams params = new SetGpsModeParams();
        params.setMode(request.getMode());
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setGpsUploadDuration(SetGpsUploadDurationRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_GPS_UPLOAD_DURATION);
        
        SetGpsUploadDurationParams params = new SetGpsUploadDurationParams();
        params.setDuration(request.getDuration());
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setGprsSwitch(SetGprsSwitchRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_GPRS_SWITCH);

        SetGprsSwitchParams params = new SetGprsSwitchParams();
        params.setState(request.getEnabled() ? 1 : 0);
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setSatelliteLockSwitch(SetSatelliteLockSwitchRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_SATELLITE_LOCK_SWITCH);

        SetSatelliteLockSwitchParams params = new SetSatelliteLockSwitchParams();
        params.setState(request.getEnabled() ? "ON" : "OFF");
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setDistanceUpload(SetDistanceUploadRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_DISTANCE_UPLOAD);
        
        SetDistanceUploadParams params = new SetDistanceUploadParams();
        params.setDistance(request.getDistance());
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setGpsSleepWork(SetGpsSleepWorkRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_GPS_SLEEP_WORK);

        SetGpsSleepWorkParams params = new SetGpsSleepWorkParams();
        // 根据sleepTime和workTime计算状态，这里简化为：如果都大于0则启用(1)，否则禁用(0)
        params.setState((request.getSleepTime() > 0 && request.getWorkTime() > 0) ? 1 : 0);
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }

    @Override
    public void setStaticUploadInterval(SetStaticUploadIntervalRequest request) {
        CommandRequest commandRequest = new CommandRequest();
        commandRequest.setImei(request.getDeviceId());
        commandRequest.setCommandType(CommandType.SET_STATIC_UPLOAD_INTERVAL);
        
        SetStaticUploadIntervalParams params = new SetStaticUploadIntervalParams();
        params.setInterval(request.getInterval());
        commandRequest.setParams(params);
        sendCommand(commandRequest);
    }
}
