package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetIpParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetIpCommand implements ICommand {

    private final SetIpParams params;
    private static final String TEMPLATE = "SERVER,%d,%s,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_IP;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getMode(), params.getAddress(), params.getPort());
    }

    @Override
    public String getDescription() {
        return String.format("%s: mode=%d, address=%s, port=%d",
            getType().getDescription(), params.getMode(), params.getAddress(), params.getPort());
    }
}