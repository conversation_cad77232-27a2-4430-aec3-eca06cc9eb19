package com.yunqu.park.iot.service.command.impl;

import com.yunqu.park.iot.model.command.CommandType;
import com.yunqu.park.iot.model.command.dto.SetSpeedAlarmParams;
import com.yunqu.park.iot.service.command.ICommand;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SetSpeedAlarmCommand implements ICommand {

    private final SetSpeedAlarmParams params;
    private static final String TEMPLATE = "SPEED,%s,%d,%d#";

    @Override
    public CommandType getType() {
        return CommandType.SET_SPEED_ALARM;
    }

    @Override
    public String build() {
        return String.format(TEMPLATE, params.getState(), params.getSpeed(), params.getMode());
    }

    @Override
    public String getDescription() {
        return String.format("%s: 状态=%s, 速度=%d km/h, 模式=%d",
            getType().getDescription(), params.getState(), params.getSpeed(), params.getMode());
    }
}