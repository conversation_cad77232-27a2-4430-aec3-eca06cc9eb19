package com.yunqu.park.iot.controller;

import com.yunqu.park.common.core.domain.R;
import com.yunqu.park.common.web.core.BaseController;
import com.yunqu.park.iot.model.command.CommandRequest;
import com.yunqu.park.iot.model.dto.BatchCommandRequest;
import com.yunqu.park.iot.model.dto.command.*;
import com.yunqu.park.iot.service.IIotCommandService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * IoT设备指令下发
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/iot/command")
public class IotCommandController extends BaseController {

    @Autowired
    private IIotCommandService iotCommandService;

    /**
     * 发送设备指令
     *
     * @param request 指令请求
     * @return 操作结果
     */
    @PostMapping("/send")
    public R<Void> sendCommand(@RequestBody CommandRequest request) {
        iotCommandService.sendCommand(request);
        return R.ok("指令已发送");
    }

    /**
     * 批量发送设备指令
     *
     * @param request 批量指令请求
     * @return 操作结果
     */
    @PostMapping("/send-batch")
    public R<Object> sendBatchCommand(@RequestBody BatchCommandRequest request) {
        return R.ok(iotCommandService.sendBatchCommand(request));
    }

    /**
     * 重启设备
     *
     * @param request 重启请求
     * @return 操作结果
     */
    @PostMapping("/reboot")
    public R<Void> reboot(@RequestBody @Valid RebootRequest request) {
        iotCommandService.reboot(request);
        return R.ok("重启指令已发送");
    }

    /**
     * 设置设备IP
     *
     * @param request IP设置请求
     * @return 操作结果
     */
    @PostMapping("/set-ip")
    public R<Void> setIp(@RequestBody @Valid SetIpRequest request) {
        iotCommandService.setIp(request);
        return R.ok("设置IP指令已发送");
    }

    /**
     * 查询设备位置
     *
     * @param request 查询请求
     * @return 操作结果
     */
    @PostMapping("/query-location")
    public R<Void> queryLocation(@RequestBody @Valid QueryLocationRequest request) {
        iotCommandService.queryLocation(request);
        return R.ok("查询位置指令已发送");
    }

    /**
     * 恢复出厂设置
     */
    @PostMapping("/factory-reset")
    public R<Void> factoryReset(@Valid @RequestBody FactoryResetRequest request) {
        iotCommandService.factoryReset(request);
        return R.ok("恢复出厂设置指令已发送");
    }

    /**
     * 查询链路地址
     */
    @PostMapping("/query-link-address")
    public R<Void> queryLinkAddress(@Valid @RequestBody QueryLinkAddressRequest request) {
        iotCommandService.queryLinkAddress(request);
        return R.ok("查询链路地址指令已发送");
    }

    /**
     * 查询参数
     */
    @PostMapping("/query-parameters")
    public R<Void> queryParameters(@Valid @RequestBody QueryParametersRequest request) {
        iotCommandService.queryParameters(request);
        return R.ok("查询参数指令已发送");
    }

    /**
     * 查询状态
     */
    @PostMapping("/query-status")
    public R<Void> queryStatus(@Valid @RequestBody QueryStatusRequest request) {
        iotCommandService.queryStatus(request);
        return R.ok("查询状态指令已发送");
    }

    /**
     * 查询版本
     */
    @PostMapping("/query-version")
    public R<Void> queryVersion(@Valid @RequestBody QueryVersionRequest request) {
        iotCommandService.queryVersion(request);
        return R.ok("查询版本指令已发送");
    }

    /**
     * 查询ICCID
     */
    @PostMapping("/query-iccid")
    public R<Void> queryIccid(@Valid @RequestBody QueryIccidRequest request) {
        iotCommandService.queryIccid(request);
        return R.ok("查询ICCID指令已发送");
    }

    /**
     * 查询网络
     */
    @PostMapping("/query-network")
    public R<Void> queryNetwork(@Valid @RequestBody QueryNetworkRequest request) {
        iotCommandService.queryNetwork(request);
        return R.ok("查询网络指令已发送");
    }

    /**
     * 查询模块版本
     */
    @PostMapping("/query-module-version")
    public R<Void> queryModuleVersion(@Valid @RequestBody QueryModuleVersionRequest request) {
        iotCommandService.queryModuleVersion(request);
        return R.ok("查询模块版本指令已发送");
    }

    /**
     * 查询震动报警灵敏度
     */
    @PostMapping("/query-vibration-sensitivity")
    public R<Void> queryVibrationSensitivity(@Valid @RequestBody QueryVibrationSensitivityRequest request) {
        iotCommandService.queryVibrationSensitivity(request);
        return R.ok("查询震动报警灵敏度指令已发送");
    }

    /**
     * 里程统计查询
     */
    @PostMapping("/query-mileage-statistics")
    public R<Void> queryMileageStatistics(@Valid @RequestBody QueryMileageStatisticsRequest request) {
        iotCommandService.queryMileageStatistics(request);
        return R.ok("里程统计查询指令已发送");
    }

    /**
     * 查询网络模式
     */
    @PostMapping("/query-network-mode")
    public R<Void> queryNetworkMode(@Valid @RequestBody QueryNetworkModeRequest request) {
        iotCommandService.queryNetworkMode(request);
        return R.ok("查询网络模式指令已发送");
    }

    /**
     * 里程清零
     */
    @PostMapping("/clear-mileage")
    public R<Void> clearMileage(@Valid @RequestBody ClearMileageRequest request) {
        iotCommandService.clearMileage(request);
        return R.ok("里程清零指令已发送");
    }

    /**
     * 删除中心号码
     */
    @PostMapping("/delete-center-number")
    public R<Void> deleteCenterNumber(@Valid @RequestBody DeleteCenterNumberRequest request) {
        iotCommandService.deleteCenterNumber(request);
        return R.ok("删除中心号码指令已发送");
    }

    /**
     * 撤防后设备状态
     */
    @PostMapping("/set-disarm-state")
    public R<Void> setDisarmState(@Valid @RequestBody SetDisarmStateRequest request) {
        iotCommandService.setDisarmState(request);
        return R.ok("撤防后设备状态指令已发送");
    }

    /**
     * 清除各种数据
     */
    @PostMapping("/clear-data")
    public R<Void> clearData(@Valid @RequestBody ClearDataRequest request) {
        iotCommandService.clearData(request);
        return R.ok("清除各种数据指令已发送");
    }

    /**
     * 设置双IP
     */
    @PostMapping("/set-dual-ip")
    public R<Void> setDualIp(@Valid @RequestBody SetDualIpRequest request) {
        iotCommandService.setDualIp(request);
        return R.ok("设置双IP指令已发送");
    }

    /**
     * 设置APN
     */
    @PostMapping("/set-apn")
    public R<Void> setApn(@Valid @RequestBody SetApnRequest request) {
        iotCommandService.setApn(request);
        return R.ok("设置APN指令已发送");
    }

    /**
     * 定时上传间隔
     */
    @PostMapping("/set-upload-interval")
    public R<Void> setUploadInterval(@Valid @RequestBody SetUploadIntervalRequest request) {
        iotCommandService.setUploadInterval(request);
        return R.ok("定时上传间隔指令已发送");
    }

    /**
     * 设置心跳间隔
     */
    @PostMapping("/set-heartbeat-interval")
    public R<Void> setHeartbeatInterval(@Valid @RequestBody SetHeartbeatIntervalRequest request) {
        iotCommandService.setHeartbeatInterval(request);
        return R.ok("设置心跳间隔指令已发送");
    }

    /**
     * 设置SOS号码
     */
    @PostMapping("/set-sos-number")
    public R<Void> setSosNumber(@Valid @RequestBody SetSosNumberRequest request) {
        iotCommandService.setSosNumber(request);
        return R.ok("设置SOS号码指令已发送");
    }

    /**
     * 删除SOS号码
     */
    @PostMapping("/delete-sos-number")
    public R<Void> deleteSosNumber(@Valid @RequestBody DeleteSosNumberRequest request) {
        iotCommandService.deleteSosNumber(request);
        return R.ok("删除SOS号码指令已发送");
    }

    /**
     * 设置中心号码
     */
    @PostMapping("/set-center-number")
    public R<Void> setCenterNumber(@Valid @RequestBody SetCenterNumberRequest request) {
        iotCommandService.setCenterNumber(request);
        return R.ok("设置中心号码指令已发送");
    }

    /**
     * 控制继电器
     */
    @PostMapping("/control-relay")
    public R<Void> controlRelay(@Valid @RequestBody ControlRelayRequest request) {
        iotCommandService.controlRelay(request);
        return R.ok("控制继电器指令已发送");
    }

    /**
     * 设置时区
     */
    @PostMapping("/set-timezone")
    public R<Void> setTimezone(@Valid @RequestBody SetTimezoneRequest request) {
        iotCommandService.setTimezone(request);
        return R.ok("设置时区指令已发送");
    }

    /**
     * 设置语言
     */
    @PostMapping("/set-language")
    public R<Void> setLanguage(@Valid @RequestBody SetLanguageRequest request) {
        iotCommandService.setLanguage(request);
        return R.ok("设置语言指令已发送");
    }

    /**
     * 设置自动布防
     */
    @PostMapping("/set-auto-arm")
    public R<Void> setAutoArm(@Valid @RequestBody SetAutoArmRequest request) {
        iotCommandService.setAutoArm(request);
        return R.ok("设置自动布防指令已发送");
    }

    /**
     * 设置角度上传
     */
    @PostMapping("/set-angle-upload")
    public R<Void> setAngleUpload(@Valid @RequestBody SetAngleUploadRequest request) {
        iotCommandService.setAngleUpload(request);
        return R.ok("设置角度上传指令已发送");
    }

    /**
     * 设置角度值
     */
    @PostMapping("/set-angle-value")
    public R<Void> setAngleValue(@Valid @RequestBody SetAngleValueRequest request) {
        iotCommandService.setAngleValue(request);
        return R.ok("设置角度值指令已发送");
    }

    /**
     * 设置上传时区
     */
    @PostMapping("/set-upload-timezone")
    public R<Void> setUploadTimezone(@Valid @RequestBody SetUploadTimezoneRequest request) {
        iotCommandService.setUploadTimezone(request);
        return R.ok("设置上传时区指令已发送");
    }

    /**
     * 设置震动灵敏度
     */
    @PostMapping("/set-vibration-sensitivity")
    public R<Void> setVibrationSensitivity(@Valid @RequestBody SetVibrationSensitivityRequest request) {
        iotCommandService.setVibrationSensitivity(request);
        return R.ok("设置震动灵敏度指令已发送");
    }

    /**
     * 设置震动报警
     */
    @PostMapping("/set-vibration-alarm")
    public R<Void> setVibrationAlarm(@Valid @RequestBody SetVibrationAlarmRequest request) {
        iotCommandService.setVibrationAlarm(request);
        return R.ok("设置震动报警指令已发送");
    }

    /**
     * 设置电源报警
     */
    @PostMapping("/set-power-alarm")
    public R<Void> setPowerAlarm(@Valid @RequestBody SetPowerAlarmRequest request) {
        iotCommandService.setPowerAlarm(request);
        return R.ok("设置电源报警指令已发送");
    }

    /**
     * 设置低电报警
     */
    @PostMapping("/set-low-battery-alarm")
    public R<Void> setLowBatteryAlarm(@Valid @RequestBody SetLowBatteryAlarmRequest request) {
        iotCommandService.setLowBatteryAlarm(request);
        return R.ok("设置低电报警指令已发送");
    }

    /**
     * 设置超速报警
     */
    @PostMapping("/set-speed-alarm")
    public R<Void> setSpeedAlarm(@Valid @RequestBody SetSpeedAlarmRequest request) {
        iotCommandService.setSpeedAlarm(request);
        return R.ok("设置超速报警指令已发送");
    }

    /**
     * 设置移动报警
     */
    @PostMapping("/set-moving-alarm")
    public R<Void> setMovingAlarm(@Valid @RequestBody SetMovingAlarmRequest request) {
        iotCommandService.setMovingAlarm(request);
        return R.ok("设置移动报警指令已发送");
    }

    /**
     * 设置ACC报警
     */
    @PostMapping("/set-acc-alarm")
    public R<Void> setAccAlarm(@Valid @RequestBody SetAccAlarmRequest request) {
        iotCommandService.setAccAlarm(request);
        return R.ok("设置ACC报警指令已发送");
    }

    /**
     * 设置急加速报警
     */
    @PostMapping("/set-harsh-acceleration-alarm")
    public R<Void> setHarshAccelerationAlarm(@Valid @RequestBody SetHarshAccelerationAlarmRequest request) {
        iotCommandService.setHarshAccelerationAlarm(request);
        return R.ok("设置急加速报警指令已发送");
    }

    /**
     * 设置急刹车报警
     */
    @PostMapping("/set-harsh-braking-alarm")
    public R<Void> setHarshBrakingAlarm(@Valid @RequestBody SetHarshBrakingAlarmRequest request) {
        iotCommandService.setHarshBrakingAlarm(request);
        return R.ok("设置急刹车报警指令已发送");
    }

    /**
     * 设置急转弯报警
     */
    @PostMapping("/set-harsh-turning-alarm")
    public R<Void> setHarshTurningAlarm(@Valid @RequestBody SetHarshTurningAlarmRequest request) {
        iotCommandService.setHarshTurningAlarm(request);
        return R.ok("设置急转弯报警指令已发送");
    }

    /**
     * 设置碰撞报警
     */
    @PostMapping("/set-collision-alarm")
    public R<Void> setCollisionAlarm(@Valid @RequestBody SetCollisionAlarmRequest request) {
        iotCommandService.setCollisionAlarm(request);
        return R.ok("设置碰撞报警指令已发送");
    }

    /**
     * 切换里程模式
     */
    @PostMapping("/switch-mileage-mode")
    public R<Void> switchMileageMode(@Valid @RequestBody SwitchMileageModeRequest request) {
        iotCommandService.switchMileageMode(request);
        return R.ok("切换里程模式指令已发送");
    }

    /**
     * 设置里程统计
     */
    @PostMapping("/set-mileage-statistics")
    public R<Void> setMileageStatistics(@Valid @RequestBody SetMileageStatisticsRequest request) {
        iotCommandService.setMileageStatistics(request);
        return R.ok("设置里程统计指令已发送");
    }

    /**
     * 设置静态休眠
     */
    @PostMapping("/set-static-sleep")
    public R<Void> setStaticSleep(@Valid @RequestBody SetStaticSleepRequest request) {
        iotCommandService.setStaticSleep(request);
        return R.ok("设置静态休眠指令已发送");
    }

    /**
     * 设置测速模式
     */
    @PostMapping("/set-speed-measurement-mode")
    public R<Void> setSpeedMeasurementMode(@Valid @RequestBody SetSpeedMeasurementModeRequest request) {
        iotCommandService.setSpeedMeasurementMode(request);
        return R.ok("设置测速模式指令已发送");
    }

    /**
     * 设置GPS模式
     */
    @PostMapping("/set-gps-mode")
    public R<Void> setGpsMode(@Valid @RequestBody SetGpsModeRequest request) {
        iotCommandService.setGpsMode(request);
        return R.ok("设置GPS模式指令已发送");
    }

    /**
     * 设置GPS上传持续时间
     */
    @PostMapping("/set-gps-upload-duration")
    public R<Void> setGpsUploadDuration(@Valid @RequestBody SetGpsUploadDurationRequest request) {
        iotCommandService.setGpsUploadDuration(request);
        return R.ok("设置GPS上传持续时间指令已发送");
    }

    /**
     * 设置GPRS开关
     */
    @PostMapping("/set-gprs-switch")
    public R<Void> setGprsSwitch(@Valid @RequestBody SetGprsSwitchRequest request) {
        iotCommandService.setGprsSwitch(request);
        return R.ok("设置GPRS开关指令已发送");
    }

    /**
     * 设置卫星锁定开关
     */
    @PostMapping("/set-satellite-lock-switch")
    public R<Void> setSatelliteLockSwitch(@Valid @RequestBody SetSatelliteLockSwitchRequest request) {
        iotCommandService.setSatelliteLockSwitch(request);
        return R.ok("设置卫星锁定开关指令已发送");
    }

    /**
     * 设置距离上传
     */
    @PostMapping("/set-distance-upload")
    public R<Void> setDistanceUpload(@Valid @RequestBody SetDistanceUploadRequest request) {
        iotCommandService.setDistanceUpload(request);
        return R.ok("设置距离上传指令已发送");
    }

    /**
     * 设置GPS休眠工作
     */
    @PostMapping("/set-gps-sleep-work")
    public R<Void> setGpsSleepWork(@Valid @RequestBody SetGpsSleepWorkRequest request) {
        iotCommandService.setGpsSleepWork(request);
        return R.ok("设置GPS休眠工作指令已发送");
    }

    /**
     * 设置静态上传间隔
     */
    @PostMapping("/set-static-upload-interval")
    public R<Void> setStaticUploadInterval(@Valid @RequestBody SetStaticUploadIntervalRequest request) {
        iotCommandService.setStaticUploadInterval(request);
        return R.ok("设置静态上传间隔指令已发送");
    }
}
